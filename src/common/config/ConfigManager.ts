import { TreeConfig } from 'common/config/TreeConfig';
import { StoneConfig } from 'common/config/StoneConfig';
import { getCdnLink } from 'common/utils';

interface Glove {
  name: string;
  color: string;
  trait: string;
  expirationTime: string;
}

type GloveId =
  | 'gloves_1001'
  | 'gloves_1002'
  | 'gloves_1003'
  | 'gloves_1004'
  | 'gloves_1005'
  | 'gloves_1006';

export type StrictGlovesCollection = {
  [key in GloveId]: Glove;
};

type CommunityInfo = {
  twitter: string;
  link: string;
};
export type GameData = {
  tree_first_combo_range: number[];
  tree_second_combo_range: number[];
  stone_first_combo_range: number[];
  stone_second_combo_range: number[];
  event_combo_count: number;
  fish_fly_speed: number;
  menus: {
    name: string;
    active: boolean;
    type: string;
    tabs: {
      type: string;
      start_time: number;
      end_time: number;
    }[];
  }[];
  //屏幕自动滑动范围
  auto_scroll_range: number;
  auto_scroll_speed: number;
  material_score_config: {
    [key: string]: number;
  };
  avatar_actions: string[];
  community: {
    TheLonelyBit: CommunityInfo;
    potato: CommunityInfo;
    wangcai: CommunityInfo;
    Pizza: CommunityInfo;
    DomoDucks: CommunityInfo;
  };
  gloves: StrictGlovesCollection;
};

export class ConfigManager {
  private static instance: ConfigManager;

  private gameData: GameData | undefined;
  private jsonLoadingCache: Map<string, boolean> = new Map<string, boolean>();
  private jsonCache: Map<string, any> = new Map<string, any>();

  static getInstance() {
    if (!ConfigManager.instance) {
      console.log('ConfigManager getInstance');
      ConfigManager.instance = new ConfigManager();
      TreeConfig.getInstance();
      StoneConfig.getInstance();
    }
    return ConfigManager.instance;
  }

  downloadConfig(url: string, cb: (data: any) => void, isCdn = true) {
    if (this.jsonLoadingCache.has(url)) {
      setTimeout(() => {
        this.downloadConfig(url, cb);
      }, 50);
      return;
    }
    const jsonObj = this.jsonCache.get(url);
    if (jsonObj) {
      cb(jsonObj);
      return;
    }
    this.jsonLoadingCache.set(url, true);
    // 如果isCdn为true，则使用cdn链接，否则使用本地链接
    const loadUrl = isCdn ? getCdnLink(url) : url;
    console.log('downloadConfig', loadUrl);
    //请求cdn文件
    fetch(loadUrl).then(async (res) => {
      console.log('downloadConfig1111', loadUrl);
      const jsonObj = await res.json();
      this.jsonLoadingCache.delete(url);
      this.jsonCache.set(url, jsonObj);
      console.log('downloadConfig222', loadUrl, jsonObj);
      cb(jsonObj);
    }).catch(error => {
      console.error(error);
    });
  }

  getData(cb: (data: GameData) => void) {
    if (this.gameData) {
      cb(this.gameData);
      return;
    }
    ConfigManager.getInstance().downloadConfig('./config/game_config.json', (data) => {
      const gameData = data as GameData;
      this.gameData = gameData;
      this.gameData.tree_first_combo_range = this.gameData.tree_first_combo_range || [
        800, 1100, 2460,
      ];
      this.gameData.tree_second_combo_range = this.gameData.tree_second_combo_range || [
        666, 766, 1792,
      ];
      this.gameData.stone_first_combo_range = this.gameData.stone_first_combo_range || [
        800, 1100, 2460,
      ];
      this.gameData.stone_second_combo_range = this.gameData.stone_second_combo_range || [
        666, 766, 1792,
      ];
      this.gameData.event_combo_count = this.gameData.event_combo_count || 3;
      this.gameData.fish_fly_speed = this.gameData.fish_fly_speed || 1;
      this.gameData.menus = this.gameData.menus || [];
      this.gameData.auto_scroll_range = this.gameData.auto_scroll_range || 0;
      this.gameData.auto_scroll_speed = this.gameData.auto_scroll_speed || 0;
      this.gameData.material_score_config = this.gameData.material_score_config || {};
      this.gameData.avatar_actions = this.gameData.avatar_actions || [
        './assets/Action/Action_05.glb',
      ];
      this.gameData.community = this.gameData.community || {};
      this.gameData.gloves = this.gameData.gloves || {};
      cb(gameData);
    });
  }
}
