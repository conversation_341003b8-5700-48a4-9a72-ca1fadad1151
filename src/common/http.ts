import { GamePlayer } from 'common/data/GamePlayer';

let GAME_REQUEST_URL = 'http://119.8.125.139:5173';
let MQ_REQUEST_URL = 'http://119.8.125.139:3001';
let CF_REQUEST_URL = 'https://beta-gapi2.satworld.io';
console.log('ENVIRONMENT', process.env.ENVIRONMENT);
if (process.env.ENVIRONMENT === 'release') {
  GAME_REQUEST_URL = 'http://***********';
  MQ_REQUEST_URL = 'http://***************:5172';
}
const local = process.env.ENVIRONMENT === 'local';

export async function post_request(path: string, player: GamePlayer, body: any): Promise<{
  code: number,
  msg: string,
  data: any,
  //任意key
  [key: string]: any,
}> {
  try {
    //https
    const headers = {
      address: player.btcAddress,
      session: player.sessionId,
      'gamekey': '311c8f0c-af16-45f5-a1fd-a3d34da4ffb2',
      'Content-Type': 'application/json',
    };

    const res = await fetch(GAME_REQUEST_URL + path, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });
    const resJson = await res.json();
    return resJson;
  } catch (e) {
    console.error('post_request error', e);
    return { code: 10001, msg: 'request error', data: null };
  }
}

async function test_request(path: string, player: GamePlayer) {
  try {
    //https
    const now = Date.now();
    const res = await fetch(CF_REQUEST_URL + path, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'address': player.btcAddress,
        'session': player.sessionId,
        'gamekey': '311c8f0c-af16-45f5-a1fd-a3d34da4ffb2',
      },
    });
    const resJson = await res.json();
    console.log('test_request', path, Date.now() - now, resJson.code);
    return res.json();
  } catch (e) {
    console.error('get_request error', e);
    return null;
  }
}

export async function get_request(path: string, player: GamePlayer) {
  try {
    // test_request(path, player).then();
    //https
    const now = Date.now();
    const res = await fetch(GAME_REQUEST_URL + path, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'address': player.btcAddress,
        'session': player.sessionId,
        'gamekey': '311c8f0c-af16-45f5-a1fd-a3d34da4ffb2',
      },
    });
    const resJson = await res.json();
    console.log('get_request', path, Date.now() - now, resJson.code);
    return resJson;
  } catch (e) {
    console.error('get_request error', e);
    return null;
  }
}


export async function post_mq(path: string, body: any) {
  if (local) {
    return;
  }
  try {
    //https
    const headers = {
      'Content-Type': 'application/json',
    };

    const res = await fetch(MQ_REQUEST_URL + path, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });

    return res.json();
  } catch (e) {
    console.error('post_request error', e);
    return null;
  }
}

export async function fishing_success(player: GamePlayer, fishRecordId: string) {
  const path = '/user-action/fa/' + fishRecordId;
  console.log('fishing_success', player.btcAddress, fishRecordId);
  return post_request(path, player, {});
}

export async function pick_up_drop(player: GamePlayer, dropId: string) {
  const path = `/user-drop-item/pup/${dropId}`;
  console.log('pick_up_drop', player.btcAddress, dropId);
  return post_request(path, player, {});
}
