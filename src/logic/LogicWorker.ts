import { PlayerManager } from 'common/mananger/PlayerManager';
import { NodeEvents, SocketEvents } from 'common/types';
import { GamePlayer } from 'common/data/GamePlayer';
import { BaseWorker } from 'common/BaseWorker';
import { game } from 'proto/generated/game_messages';
import { TreeList } from 'logic/data/TreeList';
import { RockList } from 'logic/data/RockList';
import { fishing_success, pick_up_drop } from 'common/http';
import md5 from 'md5';
import { EasterEggType, W2C_EasterEggMetaData, W2C_PacketTypes } from 'common/W2CPacket';

const IslandMapId = 2;

export interface LogicPosition {
  x: number;
  y: number;
  z: number;
  timestamp: number;
  serverReceiveTime?: number;
}

export enum PlayerLogicDataKey {
  treeList = 'treeList',
  rockList = 'rockList',
}


export class LogicWorker extends BaseWorker {
  private playerMapIdMap = new Map<string, number>();
  private playerPositionMap = new Map<string, LogicPosition>();
  private errorCount = 0;

  override async preStart() {
    await super.preStart();
    console.log('LogicWorker preStart', this.params);
    this.init();
  }

  private init() {
    PlayerManager.getInstance().register((player) => {
      }, (player) => {
        console.log('ready', player.btcAddress);
        const treeList = new TreeList(player);
        player.setData(PlayerLogicDataKey.treeList, treeList);
        const rockList = new RockList(player);
        player.setData(PlayerLogicDataKey.rockList, rockList);
      }, (btcAddress) => {
      },
    );

    this.cluster.registerRPCHandler(SocketEvents.PLAYER_LOGIC, async (data) => {
      const { pid, game_logic, btcAddress, timestamp } = data;
      const player = PlayerManager.getInstance().getPlayer(btcAddress);
      if (!player) {
        return false;
      }
      await this.logic(player, pid, game_logic, timestamp);
      return true;
    });

    this.cluster.registerRPCHandler(NodeEvents.WEB_NOTICE_PLAYER, async (notice) => {
      const { btcAddress, pid, data } = notice;
      const player = PlayerManager.getInstance().getPlayer(btcAddress);
      if (!player) {
        return false;
      }
      return await this.web_notice(player, pid, data);
    });
  }

  private async updatePlayerMapId(player: GamePlayer, mapIdKey: string) {
    switch (mapIdKey) {
      case md5(1 + player.sessionId):
        this.playerMapIdMap.set(player.btcAddress, 1);
        break;
      case md5(2 + player.sessionId):
        this.playerMapIdMap.set(player.btcAddress, 2);
        break;
      case md5(3 + player.sessionId):
        this.playerMapIdMap.set(player.btcAddress, 3);
        break;
    }
  }

  private async updatePlayerPosition(player: GamePlayer, position: LogicPosition, md5Key: string) {
    const mapId = this.playerMapIdMap.get(player.btcAddress);
    if (!mapId) {
      return;
    }
    position.serverReceiveTime = Date.now();
    const lastPosition = this.playerPositionMap.get(player.btcAddress);
    if (!lastPosition) {
      this.playerPositionMap.set(player.btcAddress, position);
      return;
    }
    if (lastPosition.timestamp >= position.timestamp) {
      console.error('updatePlayerPosition error', player.btcAddress, lastPosition.timestamp, position.timestamp);
      player.sendMessage(game.S2CPacketType.S2C_PLAYER_POSITION_UPDATE, game.PlayerPosition.create({
        x: lastPosition.x / 10,
        y: lastPosition.y / 10,
        z: lastPosition.z / 10,
      }).toJSON());
      return;
    }

    const newMd5Key = md5Key.substring(1);
    const posMd5 = md5(position.x + player.sessionId + 'pos');
    const startIndex = posMd5.indexOf(newMd5Key);
    // md5Key 包含于 posMd5
    if (Number(md5Key[0]) === startIndex) {
      this.playerPositionMap.set(player.btcAddress, position);
    } else {
      console.error('updatePlayerPosition posMd5 error', player.btcAddress, md5Key, posMd5, startIndex);
    }
    // const speed = Math.sqrt((position.x - lastPosition.x) ** 2 + (position.z - lastPosition.z) ** 2) / (position.timestamp - lastPosition.timestamp) * 1000;
    // console.error('updatePlayerPosition error', player.btcAddress, speed);
    // if (speed > 150) {
    //   this.errorCount++;
    //   if (this.errorCount > 3) {
    //     player.kick();
    //     return;
    //   }
    // } else {
    //   this.errorCount = 0;
    // }
  }

  private async web_notice(player: GamePlayer, pid: W2C_PacketTypes, data: any) {
    console.log('web_notice', pid, data);
    switch (pid) {
      case W2C_PacketTypes.TRIGGER_EASTER_EGG_MEG:
        const easterEggData = data as W2C_EasterEggMetaData;
        if (easterEggData.easterEggType === EasterEggType.ORDER_TREE) {
          const treeList = player.getData(PlayerLogicDataKey.treeList) as TreeList;
          await treeList.httpUpdateList();
        }
        break;
    }

    return true;
  }

  private async logic(player: GamePlayer, pid: game.C2SPacketType, data: any, timestamp: number) {
    const mapId = this.playerMapIdMap.get(player.btcAddress);
    const lastPosition = this.playerPositionMap.get(player.btcAddress);
    switch (pid) {
      case game.C2SPacketType.C2S_CUT_TREE:
        const treeData = data as game.ClientCutTree;
        if (mapId !== IslandMapId) {
          console.error('cutTree mapId error', player.btcAddress, treeData.treeTag, mapId);
          return;
        }
        const treeList = player.getData(PlayerLogicDataKey.treeList) as TreeList;
        if (treeList) {
          await treeList.cutTree(treeData.treeTag, treeData.useItemId, lastPosition);
        }
        break;
      case game.C2SPacketType.C2S_MINING_ROCK:
        const rockData = data as game.ClientMiningRock;
        if (mapId !== IslandMapId) {
          console.error('cutTree mapId error', player.btcAddress, rockData.rockTag, mapId);
          return;
        }
        const rockList = player.getData(PlayerLogicDataKey.rockList) as RockList;
        if (rockList) {
          await rockList.miningRock(rockData.rockTag, rockData.useItemId, lastPosition);
        }
        break;
      case game.C2SPacketType.C2S_FISHING_SUCCESS:
        if (mapId !== IslandMapId) {
          console.error('cutTree mapId error', player.btcAddress, mapId);
          return;
        }
        const fishData = data as game.ClientFishingSuccess;
        if (fishData.fishRecordId) {
          if (!lastPosition) {
            console.error('fishing_success no position', player.btcAddress);
            return;
          }
          if (!lastPosition.serverReceiveTime || Date.now() - lastPosition.serverReceiveTime > 2000) {
            console.error('fishing_success position too old', player.btcAddress, lastPosition.x, lastPosition.y, lastPosition.z);
            return;
          }
          const res = await fishing_success(player, fishData.fishRecordId);
          if (res.code == 1) {
            const data = res.data as {
              userItemId: string,
              tag: string,
              currentDurability: number,
              materialItemId: string,
              materialName: string,
              materialItemTag: string,
              materialItemQuantity: number,
              randomEventResult: { tag: string, quantity: number, eventType: string } | null,
            };
            if (data.materialItemTag) {
              player.sendMessage(game.S2CPacketType.S2C_REWARD_MATERIAL, game.UpdateMaterial.create({
                materialTag: data.materialItemTag,
              }).toJSON());
            }
            if (data.userItemId) {
              player.sendMessage(game.S2CPacketType.S2C_UPDATE_ITEM, game.UpdateItem.create({
                itemId: data.userItemId,
                durability: data.currentDurability || 0,
              }).toJSON());
            }
            if (data.randomEventResult) {
              player.sendMessage(game.S2CPacketType.S2C_REWARD_RANDOM_EVENT, game.UpdateRandomEvent.create({
                tag: data.randomEventResult.tag || '',
                quantity: String(data.randomEventResult.quantity),
                eventType: data.randomEventResult.eventType || '',
              }).toJSON());
            }
          } else {
            player.sendErrorMessage(res.msg);
          }
        }
        break;
      case game.C2SPacketType.C2S_PICK_UP_DROP:
        if (mapId !== IslandMapId) {
          console.error('cutTree mapId error', player.btcAddress, mapId);
          return;
        }
        if (!lastPosition) {
          console.error('pick_up_drop no position', player.btcAddress);
          return;
        }
        if (!lastPosition.serverReceiveTime || Date.now() - lastPosition.serverReceiveTime > 2000) {
          console.error('pick_up_drop position too old', player.btcAddress, lastPosition.x, lastPosition.y, lastPosition.z);
          return;
        }
        const dropData = data as game.ClientPickUpDrop;
        if (dropData.dropTag) {
          const res = await pick_up_drop(player, dropData.dropTag);
          if (res.code == 1) {
            const data = res.data as {
              tag: string,
              dropItemTag: string,
              quantity: number,
              isPickedUp: boolean,
              coolDown: number,
            };
            if (data.dropItemTag) {
              player.sendMessage(game.S2CPacketType.S2C_REWARD_MATERIAL, game.UpdateMaterial.create({
                materialTag: data.dropItemTag,
              }).toJSON());
            }
            player.sendMessage(game.S2CPacketType.S2C_UPDATE_PICK_UP_POINT, game.UpdatePickUpPoint.create({
              tag: dropData.dropTag,
              isPickedUp: data.isPickedUp,
              coolDown: data.coolDown,
            }).toJSON());
          } else {
            player.sendErrorMessage(res.msg);
            player.sendMessage(game.S2CPacketType.S2C_UPDATE_PICK_UP_POINT, game.UpdatePickUpPoint.create({
              tag: dropData.dropTag,
              isPickedUp: false,
              coolDown: -1,
            }).toJSON());
          }
        }
        break;
      case game.C2SPacketType.C2S_PLAYER_POSITION_UPDATE:
        const positionData = data as game.CommonMessage;
        this.updatePlayerPosition(player, {
          x: Number(positionData.messageList[0]),
          y: Number(positionData.messageList[1]),
          z: Number(positionData.messageList[2]),
          timestamp,
        }, positionData.messageList[3]).then();
        break;
      case game.C2SPacketType.C2S_PLAYER_MAP_UPDATE:
        const { messageList } = data as game.CommonMessage;
        const mapIdKey = messageList[0];
        this.updatePlayerMapId(player, mapIdKey || '').then();
        break;
    }
  }

}