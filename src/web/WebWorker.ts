import express, { Request, Response } from 'express';
import { NodeEvents, ServiceEvents } from 'common/types';
import * as dotenv from 'dotenv';
import { adminAuthMiddleware } from 'common/middleware/adminAuth';
import { BaseWorker } from 'common/BaseWorker';
import { ServiceType } from 'common/BaseService';
import { WebApiBridge } from 'web/bridge/WebApiBridge';
import { AdminRedis } from 'common/redis/AdminRedis';

// 加载环境变量
dotenv.config();

export class WebWorker extends BaseWorker {
  private app: express.Application | null = null;

  override afterStart() {
    super.afterStart();
    this.app = express();

    // 配置Express
    this.configureExpress();

    // 设置路由
    this.setupRoutes();

    console.info(`Web node initialized with ID: ${this.cluster.getNodeId()}`);
    if (this.clientPort && this.clientPort > 0) {
      this.app.listen(this.clientPort, () => {
        console.info(`Web node running on port ${this.clientPort}`);
      });
    }
  }

  private configureExpress(): void {
    if (this.app === null) {
      console.error('WebWorker configureExpress error: app is null');
      return;
    }
    // 解析JSON请求体
    this.app.use(express.json());

    // 允许跨域请求
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
      next();
    });

    // 请求日志
    this.app.use((req, res, next) => {
      console.info(`${req.method} ${req.url}`);
      next();
    });
  }

  private setupRoutes(): void {
    if (this.app === null) {
      console.error('WebWorker configureExpress error: app is null');
      return;
    }
    const bridge = new WebApiBridge(process.env.ENVIRONMENT === 'local');
    const adminRedis = new AdminRedis(this.cluster);

    // 健康检查
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({ status: 'ok', nodeId: this.cluster.getNodeId() });
    });

    // 以下路由需要管理员权限
    this.app.use('/api/notice', adminAuthMiddleware);

    // 获取所有房间信息
    this.app.get('/api/rooms', async (req: Request, res: Response) => {
      try {
        const serviceId = await this.cluster.getNodesIdByType(ServiceType.Map);
        if (!serviceId) {
          return res.status(404).json({ error: '未找到房间节点' });
        }

        const roomInfos: {
          clusterId: string;
          tag: string;
          connectCount: number;
        }[] = await this.cluster.call(serviceId, ServiceEvents.GET_WORKER_LIST, {});
        roomInfos.forEach((item: any) => {
          item.playerCount = item.connectCount;
          item.connectCount = undefined;
        });
        res.json({ rooms: roomInfos });
      } catch (error) {
        console.error('获取房间信息失败:', error);
        res.status(500).json({ error: '获取房间信息失败' });
      }
    });

    // 获取特定游戏节点的玩家信息
    this.app.get('/api/games/:gameId/players', async (req: Request, res: Response) => {
      try {
        const { gameId } = req.params;

        // 定义一个新的RPC方法来获取玩家信息
        const players = await this.cluster.call(gameId, 'GET_PLAYERS', {});

        if (!players) {
          return res.status(404).json({ error: '未找到玩家信息或游戏节点不存在' });
        }

        res.json({ players });
      } catch (error) {
        console.error('获取玩家信息失败:', error);
        res.status(500).json({ error: '获取玩家信息失败' });
      }
    });

    // 获取网关所有用户信息
    this.app.get('/api/gateway/clients', async (req: Request, res: Response) => {
      try {
        const gatewayServiceId = await this.cluster.getNodesIdByType(ServiceType.Gateway);
        if (!gatewayServiceId) {
          return res.status(404).json({ error: '未找到网关节点' });
        }

        const clients = await this.cluster.call(gatewayServiceId, ServiceEvents.GET_GATEWAY_CLIENTS, {});

        if (!clients) {
          return res.status(404).json({ error: '获取用户信息失败' });
        }

        res.json({ clients });
      } catch (error) {
        console.error('获取网关用户信息失败:', error);
        res.status(500).json({ error: '获取网关用户信息失败' });
      }
    });

    // 获取网关所有用户信息
    this.app.get('/api/gateway/history', async (req: Request, res: Response) => {
      try {
        const gatewayServiceId = await this.cluster.getNodesIdByType(ServiceType.Gateway);
        if (!gatewayServiceId) {
          return res.status(404).json({ error: '未找到网关节点' });
        }
        const data = await this.cluster.call(gatewayServiceId, ServiceEvents.GET_GATEWAY_HISTORY, {});

        if (!data) {
          return res.status(404).json({ error: '获取用户信息失败' });
        }

        res.json(data);
      } catch (error) {
        console.error('获取网关用户信息失败:', error);
        res.status(500).json({ error: '获取网关用户信息失败' });
      }
    });


    // 接受聊天消息接口。post请求
    this.app.post('/api/chat/message', async (req: Request, res: Response) => {
      try {
        const { chatId } = req.body;
        const chatServiceId = await this.cluster.getNodesIdByType(ServiceType.Chat);
        if (!chatServiceId) {
          return res.status(404).json({ error: '未找到聊天服务' });
        }

        const messageId = await this.cluster.call(chatServiceId, NodeEvents.WEB_NOTICE_CHAT, {
          chatId,
          data: req.body,
        });
        if (messageId) {
          res.json({ code: 1, msg: 'ok', data: { messageId } });
        } else {
          res.json({ code: 0, msg: 'fail' });
        }
      } catch (error) {
        console.error('发送聊天消息失败:', error);
        res.status(500).json({ error: '发送聊天消息失败' });
      }
    });

    // 通知玩家的消息
    bridge.registerPost(this.app, '/api/notice/player', async (req: Request, res: Response) => {
      try {
        console.log('api/notice/player', req.body);
        const gateServiceId = await this.cluster.getNodesIdByType(ServiceType.Gateway);
        if (!gateServiceId) {
          return res.status(404).json({ error: '未找到网关节点' });
        }
        await this.cluster.send(gateServiceId, NodeEvents.WEB_NOTICE_PLAYER, req.body);
        res.json({ code: 1, msg: 'ok' });
      } catch (error) {
        console.error('发送聊天消息失败:', error);
        res.status(500).json({ error: '发送聊天消息失败' });
      }
    });

    // 通知玩家的消息
    bridge.registerPost(this.app, '/api/notice/service', async (req: Request, res: Response) => {
      try {
        console.log('api/notice/service', req.body);
        const gateServiceId = await this.cluster.getNodesIdByType(ServiceType.Gateway);
        if (!gateServiceId) {
          return res.status(404).json({ error: '未找到网关节点' });
        }
        await this.cluster.send(gateServiceId, NodeEvents.WEB_NOTICE_SERVICE, req.body);
        res.json({ code: 1, msg: 'ok' });
      } catch (error) {
        console.error('发送聊天消息失败:', error);
        res.status(500).json({ error: '发送聊天消息失败' });
      }
    });

    // 通知玩家的消息
    bridge.registerPost(this.app, '/api/notice/message', async (req: Request, res: Response) => {
      try {
        console.log('api/notice/message', req.body);
        const chatServiceId = await this.cluster.getNodesIdByType(ServiceType.Chat);
        if (!chatServiceId) {
          return res.status(404).json({ error: '未找到聊天服务节点' });
        }
        await this.cluster.send(chatServiceId, NodeEvents.WEB_NOTICE_SYSTEM_MESSAGE, req.body);
        res.json({ code: 1, msg: 'ok' });
      } catch (error) {
        console.error('发送聊天消息失败:', error);
        res.status(500).json({ error: '发送聊天消息失败' });
      }
    });

    // 通知房间的消息
    bridge.registerPost(this.app, '/api/notice/room', async (req: Request, res: Response) => {
      try {
        const { mapId, pid, data } = req.body;
        console.log('api/notice/room', req.body);
        const mapServiceId = await this.cluster.getNodesIdByType(ServiceType.Map);
        if (!mapServiceId) {
          return res.status(404).json({ error: '未找到地图服务' });
        }

        await this.cluster.send(mapServiceId, NodeEvents.WEB_NOTICE_ROOM, {
          mapId,
          pid,
          data,
        });
        res.json({ code: 1, msg: 'ok' });
      } catch (error) {
        console.error('发送聊天消息失败:', error);
        res.status(500).json({ error: '发送聊天消息失败' });
      }
    });

    // 获取admin 数据
    this.app.get('/api/admin/data', async (req: Request, res: Response) => {
      try {
        const data = await adminRedis.getAdminData();
        res.json(data);
      } catch (error) {
        console.error('获取admin数据失败:', error);
        res.status(500).json({ error: '获取admin数据失败' });
      }
    });

    // 开服
    this.app.post('/api/admin/service/open', async (req: Request, res: Response) => {
      try {
        await adminRedis.openService();
        res.json({ code: 1, msg: 'ok' });
      } catch (error) {
        console.error('开服失败:', error);
        res.status(500).json({ error: '开服失败' });
      }
    });
    // 关闭服务
    this.app.post('/api/admin/service/close', async (req: Request, res: Response) => {
      try {
        const gatewayServiceId = await this.cluster.getNodesIdByType(ServiceType.Gateway);
        if (!gatewayServiceId) {
          return res.status(404).json({ error: '未找到网关节点' });
        }
        await this.cluster.send(gatewayServiceId, NodeEvents.WEB_SERVICE_CLOSE, {});
        await adminRedis.closeService();
        res.json({ code: 1, msg: 'ok' });
      } catch (error) {
        console.error('关闭服务失败:', error);
        res.status(500).json({ error: '关闭服务失败' });
      }
    });
    // 添加白名单
    this.app.post('/api/admin/white/add', async (req: Request, res: Response) => {
      try {
        const { btcAddress } = req.body;
        const code = await adminRedis.addWhiteAddress(btcAddress);
        res.json({ code, msg: code === 1 ? 'ok' : 'already in white list' });
      } catch (error) {
        console.error('添加白名单失败:', error);
        res.status(500).json({ error: '添加白名单失败' });
      }
    });
    // 删除白名单
    this.app.post('/api/admin/white/delete', async (req: Request, res: Response) => {
      try {
        const { btcAddress } = req.body;
        const code = await adminRedis.deleteWhiteAddress(btcAddress);
        res.json({ code, msg: code === 1 ? 'ok' : 'not in white list' });
      } catch (error) {
        console.error('删除白名单失败:', error);
        res.status(500).json({ error: '删除白名单失败' });
      }
    });
    // 添加黑名单
    this.app.post('/api/admin/black/add', async (req: Request, res: Response) => {
      try {
        const { btcAddress } = req.body;
        const gatewayServiceId = await this.cluster.getNodesIdByType(ServiceType.Gateway);
        if (!gatewayServiceId) {
          return res.status(404).json({ error: '未找到网关节点' });
        }
        await this.cluster.send(gatewayServiceId, NodeEvents.GAME_PLAYER_KICK, btcAddress);
        const code = await adminRedis.addBlackAddress(btcAddress);
        res.json({ code, msg: code === 1 ? 'ok' : 'already in black list' });
      } catch (error) {
        console.error('添加黑名单失败:', error);
        res.status(500).json({ error: '添加黑名单失败' });
      }
    });
    // 删除黑名单
    this.app.post('/api/admin/black/delete', async (req: Request, res: Response) => {
      try {
        const { btcAddress } = req.body;
        const code = await adminRedis.deleteBlackAddress(btcAddress);
        res.json({ code, msg: code === 1 ? 'ok' : 'not in black list' });
      } catch (error) {
        console.error('删除黑名单失败:', error);
        res.status(500).json({ error: '删除黑名单失败' });
      }
    });

    // 404处理
    this.app.use((req: Request, res: Response) => {
      res.status(404).json({ error: '未找到请求的资源' });
    });
  }
}
